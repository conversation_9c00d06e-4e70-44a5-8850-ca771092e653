"""
数据模型 - SQLModel定义
🎯 核心价值：类型安全的数据库模型，支持Pydantic验证和SQLAlchemy ORM
📦 模块：用户、项目、颜色数据、网格数据、版本管理等
"""

from .black_cell_data import (
    BlackCellData,
    BlackCellDataCreate,
    BlackCellDataResponse,
    BlackCellDataUpdate,
)
from .color_data import ColorData, ColorDataCreate, ColorDataResponse, ColorDataUpdate

from .grid_data import GridData, GridDataCreate, GridDataResponse, GridDataUpdate

# 导入所有模型以确保它们被注册到SQLModel.metadata
# from .user import User, UserCreate, UserUpdate, UserResponse  # 已移除
from .project import Project, ProjectCreate, ProjectResponse, ProjectUpdate

from .version import Version, VersionCreate, VersionResponse, VersionUpdate

__all__ = [
    # User models（已移除）
    # "User",
    # "UserCreate",
    # "UserUpdate",
    # "UserResponse",
    # Project models
    "Project",
    "ProjectCreate",
    "ProjectUpdate",
    "ProjectResponse",
    # ColorData models
    "ColorData",
    "ColorDataCreate",
    "ColorDataUpdate",
    "ColorDataResponse",
    # GridData models
    "GridData",
    "GridDataCreate",
    "GridDataUpdate",
    "GridDataResponse",
    # BlackCellData models
    "BlackCellData",
    "BlackCellDataCreate",
    "BlackCellDataUpdate",
    "BlackCellDataResponse",
    # Version models
    "Version",
    "VersionCreate",
    "VersionUpdate",
    "VersionResponse",


]
